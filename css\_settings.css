/* 包含 Pagetalk 插件设置界面的所有样式，包括通用设置、Agent 管理、模型配置、表单元素、滑块、按钮、提示框等。 */

:root {
    --toast-bottom-base: 120px; /* 常规提示的基础底部位置 */
    --toast-bottom-error: 120px; /* 错误提示的特殊底部位置 */
    --toast-visual-offset-y: -10px; /* toast.show 状态下的向上视觉偏移 */
}

/* 设置界面样式 */
#settings {
    padding: clamp(var(--spacing-md), 4vw, var(--spacing-xl));
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color); /* Settings tab uses main background */
}

#settings .settings-container { 
    /* background-color: var(--background-color); /* Removed, settings section has this */
    padding: 0; 
    max-width: 100%;
    width: 100%; 
    overflow-x: hidden;
    flex: 1; 
    display: flex; 
    flex-direction: column;
    box-sizing: border-box;
}

/* 设置子内容区域标题 */
.settings-sub-content h2 {
    font-size: clamp(16px, 4vw, 18px); 
    margin-bottom: var(--spacing-lg); 
    color: var(--text-color); 
    font-weight: 600;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

/* 设置内部导航 */
.settings-nav {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
    flex-shrink: 0; 
    align-items: center; 
}

.settings-nav-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    border: none;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: clamp(12px, 3.5vw, 14px);
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(1);
    position: relative;
    overflow: hidden;
}

.settings-nav-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(49, 123, 245, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.settings-nav-btn:hover {
    color: var(--primary-color);
    background-color: var(--button-hover-bg);
    transform: scale(1.02);
}

.settings-nav-btn:hover::before {
    width: 100%;
    height: 100%;
}

.settings-nav-btn:active {
    transform: scale(0.98);
}

.settings-nav-btn.active {
    color: var(--primary-color);
    font-weight: 600;
    background-color: rgba(76, 110, 245, 0.15);
    transform: scale(1);
}
/* Push close button to the right */
#close-panel-settings {
    margin-left: auto;
}


/* 设置子内容区域 */
.settings-sub-content {
    display: none;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--spacing-sm) 0;
    min-height: 0;
    opacity: 0;
    transform: translateY(0); /* 修改：移除初始位移避免溢出 */
    transition: opacity 0.2s ease; /* 简化：只使用透明度过渡 */
}
.settings-sub-content.active {
    display: block;
    animation: settingsContentFadeIn 0.2s ease forwards; /* 简化动画 */
}

@keyframes settingsContentFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 通用设置特定样式 */
#settings-general .setting-group {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color); 
}
#settings-general .setting-group:last-of-type {
    border-bottom: none;
    padding-bottom: 0;
}
.theme-setting { /* For theme toggle within general settings if ever moved back */
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}
.theme-setting label {
    margin-bottom: 0; 
}

.export-setting label {
    margin-bottom: var(--spacing-sm); 
}
.export-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}
.export-controls select {
    flex: 1; 
    max-width: 150px; 
}
.export-controls .action-button { /* Specific styling for export button */
    background-color: var(--primary-color); 
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    width: auto; 
    height: auto;
    font-size: clamp(12px, 3vw, 13px);
    transition: all 0.2s ease;
}
.export-controls .action-button:hover {
    background-color: var(--secondary-color);
    color: white; 
}


/* 设置组 (通用表单元素容器) */
.setting-group {
    margin-bottom: var(--spacing-lg); /* Default bottom margin for groups */
}
.setting-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: clamp(12px, 3vw, 13px);
    font-weight: 500;
    color: var(--text-color);
}
.setting-group input[type="text"],
.setting-group input[type="password"],
.setting-group input[type="number"],
.setting-group textarea,
.setting-group select {
    width: 100%;
    padding: clamp(var(--spacing-sm), 2.5vw, var(--spacing-md));
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg); /* 16px 圆角 */
    font-size: clamp(12px, 3vw, 13px);
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: var(--input-background); /* Use variable */
    transform: translateY(0);
}

/* 代理地址输入框特定样式 */
#proxy-address-input {
    width: 30% !important;
}

/* 代理地址提示文本样式 */
#proxy-address-input + .hint {
    padding-left: var(--spacing-xs); /* 减少左边距，让文本往左一点 */
    font-style: italic; /* 变成斜体 */
}
.setting-group input:focus,
.setting-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1), 0 2px 8px rgba(49, 123, 245, 0.05);
    background-color: var(--card-background); /* Use variable */
    transform: translateY(-1px);
}

.setting-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 110, 245, 0.1);
    background-color: var(--card-background); /* Use variable */
    transform: none; /* 取消选择框的上抬效果 */
}

/* 输入时的微妙反馈 */
.setting-group input:not(:placeholder-shown),
.setting-group textarea:not(:placeholder-shown) {
    border-color: rgba(49, 123, 245, 0.3);
}
.setting-group textarea {
    min-height: 80px;
    resize: vertical;
}
.hint {
    font-size: clamp(10px, 2.5vw, 11px);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    padding-left: var(--spacing-md); /* Add padding to move text to the right */
}
.hint a { /* Style for links inside hints */
    color: var(--primary-color);
    text-decoration: none;
}
.hint a:hover {
    text-decoration: underline;
}


/* 滑块容器调整 */
.slider-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin: var(--spacing-sm) 0;
}
.slider-container input[type="range"] {
    flex: 1;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: var(--border-color);
    border-radius: var(--radius-full);
    outline: none;
    position: relative;
    margin: 10px 0; 
}
.slider-container input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--card-background); /* Use variable */
    border: 2px solid var(--primary-color);
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-top: -6px; 
}
.slider-container input[type="range"]::-webkit-slider-thumb:hover,
.slider-container input[type="range"]:active::-webkit-slider-thumb {
    transform: scale(1.2);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}
.slider-container span { /* Slider value display */
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
    font-size: clamp(12px, 3.5vw, 14px);
}

/* 复选框组 */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
}
.checkbox-group input[type="checkbox"] {
    width: clamp(16px, 4.5vw, 18px);
    height: clamp(16px, 4.5vw, 18px);
    accent-color: var(--primary-color);
}

/* 模型设置特定外框 */
.model-settings-group-box {
    border-radius: var(--radius-lg); 
    margin-bottom: var(--spacing-lg); 
    padding: clamp(var(--spacing-sm), 2.5vw, var(--spacing-md));
    border: 1px solid var(--border-color);
    width: 100%;
    background-color: var(--card-background); /* Give it a card background */
}
.model-settings-group-box .setting-group {
    border-bottom: none;
    margin-bottom: var(--spacing-md); 
    padding-bottom: 0; 
}
.model-settings-group-box .setting-group:last-child {
    margin-bottom: 0;
}

/* 模型设置保存按钮区域 */
.model-save-area {
    display: flex;
    flex-direction: column; 
    align-items: flex-end; 
    margin-top: var(--spacing-lg); 
}
.model-save-area .save-btn {
    margin-top: 0; 
}
.model-save-area .connection-status {
     margin-top: var(--spacing-sm); 
     width: auto; 
     text-align: right; 
}
.save-btn, .test-btn { /* General save/test button style */
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: clamp(var(--spacing-xs), 1.5vw, var(--spacing-sm)) clamp(var(--spacing-md), 3vw, var(--spacing-lg));
    font-size: clamp(12px, 3.5vw, 14px);
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    transform: scale(1);
    position: relative;
    overflow: hidden;
}

.save-btn::before, .test-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.save-btn {
    margin-top: var(--spacing-lg); /* Default margin for save buttons */
}
.save-btn:hover, .test-btn:hover {
    transform: scale(1.02); /* 移除 translateY，只保留缩放 */
    box-shadow: 0 2px 8px rgba(76, 110, 245, 0.2); /* 减少阴影强度 */
}

.save-btn:hover::before, .test-btn:hover::before {
    width: 100%;
    height: 100%;
}

.save-btn:active, .test-btn:active {
    transform: scale(0.98);
    box-shadow: 0 1px 3px rgba(76, 110, 245, 0.2);
}
.test-btn { /* Specific for test button, often beside save */
     background-color: var(--warning-color); 
     margin-right: var(--spacing-md); 
}
.test-btn:hover {
     background-color: #ffc107; /* Lighter warning color on hover */
     box-shadow: 0 2px 8px rgba(230, 119, 0, 0.2); /* Warning color shadow */
}

/* 连接状态提示 (模型设置页) */
.connection-status {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md); /* Adjusted padding */
    border-radius: var (--radius-md);
    font-size: 13px;
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}
.connection-status.success {
    display: block;
    opacity: 1;
    transform: translateY(0);
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--success-color); 
    border: 1px solid rgba(52, 168, 83, 0.3);
}
.connection-status.error {
    display: block;
    opacity: 1;
    transform: translateY(0);
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--error-color); 
    border: 1px solid rgba(234, 67, 53, 0.3);
}



/* 助手列表样式 - 重构版 */
.agents-list-container {
    background-color: var(--card-background);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    padding: clamp(var(--spacing-sm), 2.5vw, var(--spacing-md));
    border: 1px solid var(--border-color);
    width: 100%;
    box-sizing: border-box;
    /* 重要：防止内容溢出 */
    overflow: hidden;
}

.agents-list-header {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.agents-list-header h3 {
    font-size: clamp(12px, 3.5vw, 14px);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

/* Agent 操作按钮栏 */
.agent-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.agent-actions-left,
.agent-actions-right {
    display: flex;
    gap: var(--spacing-sm);
}

/* Agent 操作按钮的通用样式 */
.agent-action-button {
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: clamp(11px, 2.5vw, 12px);
    transition: all 0.2s ease;
    min-width: 50px; /* 缩小最小宽度 */
    text-align: center;
    display: flex; /* 使文本居中 */
    justify-content: center; /* 使文本居中 */
    align-items: center; /* 使文本居中 */
    height: 28px; /* 调整高度与导入导出按钮一致 */
    box-sizing: border-box; /* 确保padding和border包含在height内 */
}

.agent-action-button:hover {
    background-color: var(--secondary-color); /* 悬停时使用 secondary-color */
    color: white;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(49, 123, 245, 0.3);
}

.agent-action-button:active {
    transform: scale(0.98);
}

/* 添加按钮的特定样式 */
.add-agent-button {
    background-color: var(--success-color); /* 绿色 */
    min-width: 50px;
    height: 29px; /* 确保高度一致 */
}

.add-agent-button:hover {
    background-color: #45a049; /* 更好看的绿色悬停效果 */
    box-shadow: 0 3px 8px rgba(69, 160, 73, 0.25); /* 调整阴影颜色和强度 */
}

/* 助手列表容器 - 最终优化版 */
.agents-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动条 */
    min-height: 100px;
    max-height: 400px;
    /* 简化：只保留必要的内边距 */
    padding: var(--spacing-xs) 0 var(--spacing-md) 0;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.agents-list::-webkit-scrollbar {
    width: 6px;
}

.agents-list::-webkit-scrollbar-track {
    background: transparent;
}

.agents-list::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

.agents-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

/* Agent 列表项 - 完美适配版 */
.agent-item {
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-md);
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    width: calc(100% - 4px); /* 为阴影预留 2px 左右空间 */
    margin: 2px; /* 为上下阴影预留空间 */
    transform: scale(1);
    opacity: 1;
}

.agent-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(76, 110, 245, 0.15);
    transform: translateY(-1px) scale(1.005);
    z-index: 1;
}
.agent-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: clamp(var(--spacing-xs), 1.5vw, var(--spacing-sm)) clamp(var(--spacing-sm), 2.5vw, var(--spacing-md));
    cursor: pointer;
    transition: background-color 0.2s ease;
}
.agent-item-header:hover {
    background-color: var(--button-hover-bg); /* Primary color transparent hover */
}
.agent-item-name { 
    font-weight: 500;
    color: var(--text-color);
    font-size: clamp(12px, 3vw, 13px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1; 
    margin-right: var(--spacing-sm); 
}
.expand-icon {
    flex-shrink: 0;
    margin-left: auto; 
    margin-right: var(--spacing-sm);
    color: var(--text-secondary);
    transition: transform 0.3s ease;
    display: flex; 
    align-items: center;
}
.agent-item.expanded .expand-icon {
    transform: rotate(90deg);
}
.agent-item-actions {
    display: flex;
    gap: var(--spacing-xs);
    flex-shrink: 0; 
}
.agent-item-actions .delete-btn { /* Specific styles for delete button in agent item */
     width: clamp(22px, 6.5vw, 26px);
     height: clamp(22px, 6.5vw, 26px);
     border-radius: var(--radius-full);
     border: none;
     background-color: transparent;
     display: flex;
     align-items: center;
     justify-content: center;
     cursor: pointer;
     transition: all 0.2s ease;
     color: var(--text-secondary);
     opacity: 0.7; /* Slightly transparent by default */
     padding: 0; 
}
.agent-item-actions .delete-btn:hover {
     opacity: 1;
     color: var(--error-color);
     background-color: rgba(234, 67, 53, 0.1); /* Error color transparent hover */
}
.agent-item-header:hover .agent-item-actions .delete-btn { /* Make fully visible on header hover */
    opacity: 1;
}

/* Agent 项内容体 (可折叠) */
.agent-item-body {
    max-height: 0;
    overflow: hidden;
    padding: 0 var(--spacing-lg);
    border-top: 1px solid transparent;
    background-color: #fdfdfd; /* Slightly different background from card to differentiate */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
}
.agent-item.expanded .agent-item-body {
    max-height: 800px; /* 足够大的高度 */
    padding: var(--spacing-md) var(--spacing-lg);
    border-top-color: var(--border-color);
    opacity: 1;
}
.agent-item-body .setting-group {
    margin-bottom: var(--spacing-md); 
    padding-bottom: var(--spacing-md); /* Add padding for visual separation if no border */
    /* border-bottom: 1px solid var(--border-color); /* Optional: line between groups inside body */
}
.agent-item-body .setting-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}
.agent-item-body .setting-group label {
    margin-bottom: var(--spacing-xs); 
}
.agent-item-body .setting-group input[type="text"],
.agent-item-body .setting-group input[type="number"],
.agent-item-body .setting-group textarea {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
    border-radius: var(--radius-lg); 
    border-color: var(--border-color); /* Ensure correct border in light mode */
}
.agent-item-body .setting-group textarea {
    min-height: 120px; 
}
.agent-item-body .slider-container {
    gap: var(--spacing-sm); 
}
.agent-item-body .slider-container input[type="range"] {
    background: var(--border-color); /* Correct slider track in light mode */
}


/* 删除确认对话框样式 - 现在使用统一的 _chat.css 中的 .dialog-overlay 和 .dialog-content 样式 */
/* 保留删除对话框特定的样式调整 */
#delete-confirm-dialog .dialog-content {
    max-width: 320px; /* 删除对话框稍微小一点 */
}

#delete-confirm-dialog .dialog-content h3 {
    font-size: 16px;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

#delete-confirm-dialog .dialog-content p {
    margin-bottom: var(--spacing-lg);
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
}

/* 删除对话框动作按钮的字体大小稍微调整 */
#delete-confirm-dialog .dialog-actions button {
    font-size: 13px;
}


/* 状态提示 Toast */
.toast {
    position: fixed;
    bottom: var(--toast-bottom-base); /* 使用变量 */
    left: 50%;
    transform: translateX(-50%); /* 初始 transform，show 状态会覆盖 Y 方向 */
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--card-background);
    color: var(--text-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    font-size: 13px;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease, bottom 0.3s ease; /* 添加 transform 和 bottom 过渡 */
    pointer-events: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
}
.toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(var(--toast-visual-offset-y)); /* 使用变量 */
}
.toast.success {
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(52, 168, 83, 0.3);
    bottom: var(--toast-bottom-base); /* 明确使用基础位置变量 */
}
.toast.error {
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(234, 67, 53, 0.3);
    bottom: var(--toast-bottom-error) !important; /* 使用变量, 保留 important */
}
.toast.info { /* For informational toasts like "Streaming Aborted" */
    background-color: rgba(76, 110, 245, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(76, 110, 245, 0.3);
    bottom: var(--toast-bottom-base); /* info 提示也使用基础位置 */
}


/* API Key 输入框样式 */
.api-key-input-container {
    position: relative;
    display: flex;
    align-items: center;
}
.api-key-input-container input { /* Specifically target input within this container */
    padding-right: 30px; /* Space for the toggle button */
}
.toggle-api-key-button {
    position: absolute;
    right: 8px; 
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
    outline: none;
    color: var(--text-secondary); /* Default color */
}
.toggle-api-key-button:hover {
    color: var(--text-color); /* Darker on hover */
}


/* 隐藏数字输入框的上下箭头 */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] { /* For Firefox */
  -moz-appearance: textfield; 
  appearance: textfield; /* For other modern browsers */
}


/* --- 可拖动主题切换按钮 --- */
#theme-toggle-btn {
    display: none; /* Hide by default, shown via JS for settings tab */
    position: absolute; 
    right: var(--spacing-lg); 
    bottom: 80px; 
    left: auto; 
    top: auto; 
    cursor: grab; 
    z-index: 99; 
    width: 48px; 
    height: 48px; 
    padding: 12px; 
    transition: background-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    /* Inherits .icon-btn styles for background, color, border-radius, etc. */
    /* JS will add .icon-btn and .theme-toggle-btn classes */
}
#theme-toggle-btn:active {
    cursor: grabbing; 
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); 
}
/* JS controls display based on active tab */
