# PageTalk 代理功能设置指南

## 概述

PageTalk 现在支持代理功能，可以通过HTTP和SOCKS代理服务器访问Gemini API。这对于在受限网络环境中使用PageTalk非常有用。

## 支持的代理类型

- **HTTP代理**: `http://proxy-server:port`
- **HTTPS代理**: `https://proxy-server:port`
- **SOCKS4代理**: `socks4://proxy-server:port`
- **SOCKS5代理**: `socks5://proxy-server:port`

## 设置步骤

1. 打开PageTalk扩展面板
2. 点击底部的"设置"标签
3. 在"常规设置"部分找到"代理地址"输入框
4. 输入你的代理服务器地址，例如：
   - `http://127.0.0.1:7890`
   - `socks5://127.0.0.1:1080`
5. 点击"测试代理"按钮验证连接
6. 如果测试成功，代理设置将自动保存并应用

## 代理测试功能

新增的"测试代理"按钮会：
- 验证代理地址格式
- 尝试通过代理连接到Google服务器
- 显示连接结果（成功/失败）

## 工作原理

### 1. Chrome代理设置
PageTalk使用Chrome的`chrome.proxy` API来设置浏览器级别的代理，这会影响：
- 扩展内的所有网络请求
- Gemini API调用
- PDF文件下载

### 2. 请求路由
所有的API请求都会通过配置的代理服务器：
- 主面板的聊天功能
- 划词助手功能
- PDF内容提取

### 3. 错误处理
如果代理连接失败，系统会：
- 显示错误信息
- 自动回退到直接连接
- 记录详细的错误日志

## 常见问题

### Q: 为什么代理测试失败？
A: 可能的原因：
- 代理服务器地址错误
- 代理服务器未运行
- 网络连接问题
- 代理服务器需要认证（暂不支持）

### Q: 如何禁用代理？
A: 清空代理地址输入框即可禁用代理功能。

### Q: 支持需要用户名密码的代理吗？
A: 当前版本暂不支持需要认证的代理服务器。

## 技术实现

### 代理配置
```javascript
const proxyConfig = {
    mode: "fixed_servers",
    rules: {
        singleProxy: {
            scheme: "http", // 或 https, socks4, socks5
            host: "127.0.0.1",
            port: 7890
        },
        bypassList: ["<local>"]
    }
};
```

### API请求处理
所有API请求都通过统一的`makeApiRequest`函数处理，该函数会：
1. 检查代理设置
2. 应用代理配置
3. 发起请求
4. 处理错误

## 故障排除

1. **检查代理服务器状态**
   - 确保代理服务器正在运行
   - 验证端口号正确

2. **检查网络连接**
   - 尝试直接访问代理服务器
   - 检查防火墙设置

3. **查看控制台日志**
   - 打开浏览器开发者工具
   - 查看Console标签中的错误信息

4. **重置代理设置**
   - 清空代理地址
   - 重新输入正确的代理信息

## 更新日志

### v3.5.1
- 新增代理支持功能
- 添加代理测试按钮
- 改进错误处理和用户反馈
- 支持HTTP、HTTPS、SOCKS4、SOCKS5代理
