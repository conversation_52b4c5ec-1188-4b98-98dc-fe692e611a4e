<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagetalk</title>
    <link rel="stylesheet" href="../css/sidepanel.css">
    <!-- Highlight.js Themes -->
    <link rel="stylesheet" href="../css/github.min.css">
    <link rel="stylesheet" href="../css/github-dark-dimmed.min.css">
    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="../css/katex.min.css">
    <!-- Highlight.js Core & Languages -->
    <script src="../js/lib/highlight.min.js"></script>
    <script src="../js/lib/python.min.js"></script>
    <script src="../js/lib/r.min.js"></script>
    <script src="../js/lib/sql.min.js"></script>
    <script type="module" src="../js/lib/java.min.js"></script>
    <script type="module" src="../js/lib/javascript.min.js"></script>
    <script src="../js/lib/json.min.js"></script>
    <!-- 使用本地markdown-it库 -->
    <script src="../js/lib/markdown-it.min.js"></script>
    <!-- 添加我们的Markdown渲染器 -->
    <script src="../js/markdown-renderer.js"></script>
</head>
<body>
    <div class="container">
        <main>
            <!-- 聊天界面 -->
            <section id="chat" class="tab-content active">
                <div class="chat-container">
                    <div class="chat-header">
                        <div class="chat-model-selector">
                            <label for="chat-model-selection">模型：</label>
                            <select id="chat-model-selection" aria-label="选择模型">
                                <!-- 这里的选项会通过 JavaScript 动态填充 -->
                            </select>
                        </div>
                        <!-- 添加助手选择器 -->
                        <div class="chat-agent-selector">
                            <label for="chat-agent-selection">助手：</label>
                            <select id="chat-agent-selection" aria-label="选择助手">
                                <!-- 这里的选项会通过 JavaScript 动态填充 -->
                            </select>
                        </div>
                        <button id="clear-context" title="清除上下文和聊天记录" class="action-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                            </svg>
                        </button>
                        <!-- 添加关闭面板按钮 -->
                        <button id="close-panel" title="关闭面板" class="action-button close-panel-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="chat-messages" id="chat-messages">
                        <div class="welcome-message">
                            <h2>欢迎使用 PageTalk :) </h2>
                            <div class="quick-actions">
                                <button id="summarize-page" class="quick-action-btn">总结一下</button>
                            </div>
                        </div>
                    </div>
                    <!-- 修改图片预览区域为多图片网格布局 -->
                    <div class="image-preview-container" id="image-preview-container" style="display: none;">
                        <div class="images-grid" id="images-grid">
                            <!-- 图片项将动态添加到这里 -->
                        </div>
                    </div>
                    <!-- 视频预览区域 -->
                    <div class="video-preview-container" id="video-preview-container" style="display: none;">
                        <div class="videos-grid" id="videos-grid">
                            <!-- 视频项将动态添加到这里 -->
                        </div>
                    </div>
                    <!-- 图片全屏预览模态框 -->
                    <div id="image-modal" class="image-modal">
                        <span class="close-modal">&times;</span>
                        <img id="modal-image" class="modal-content" alt="Full size image preview">
                    </div>
                    <!-- YouTube URL 输入对话框 -->
                    <div id="youtube-url-dialog" class="dialog-overlay" style="display:none;">
                        <div class="dialog-content">
                            <h3 data-i18n="addYoutubeVideoTitle"></h3>
                            <p data-i18n="enterYoutubeLinkPrompt"></p>
                            <input type="url" id="youtube-url-input" data-i18n-placeholder="youtubeLinkPlaceholder" />
                            <div class="dialog-actions">
                                <button id="cancel-youtube" class="cancel-btn" data-i18n="cancelButton"></button>
                                <button id="confirm-youtube" class="confirm-btn" data-i18n="addButton"></button>
                            </div>
                        </div>
                    </div>
                    <!-- 新增：聊天界面状态消息区域 -->
                    <div id="chat-status-message" class="chat-status"></div>
                    <div class="chat-input">
                        <!-- 添加图片上传按钮 -->
                        <button id="upload-image" title="上传图片" class="upload-image-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                <path d="M1.5 2A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2h-13zm13 1a.5.5 0 0 1 .5.5v6l-3.775-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12v.54A.505.505 0 0 1 1 12.5v-9a.5.5 0 0 1 .5-.5h13z"/>
                            </svg>
                        </button>
                        <!-- 添加YouTube URL输入按钮 -->
                        <button id="add-youtube-url" data-i18n-title="addYoutubeLinkTitle" class="youtube-button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z"/>
                            </svg>
                        </button>
                        <!-- 隐藏的文件输入框 -->
                        <input type="file" id="file-input" accept="image/*" multiple style="display: none;">
                        <textarea id="user-input" placeholder="..."></textarea>
                        <button id="send-message" title="发送消息">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M15.854.146a.5.5 0 0 1 .11.54l-5.819 14.547a.75.75 0 0 1-1.329.124l-3.178-4.995L.643 7.184a.75.75 0 0 1 .124-1.33L15.314.037a.5.5 0 0 1 .54.11v-.001ZM6.636 10.07l2.761 4.338L14.13 2.576 6.636 10.07Zm6.787-8.201L1.591 6.602l4.339 2.76 7.494-7.493Z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 设置界面 -->
            <section id="settings" class="tab-content">
                <div class="settings-container">
                    <!-- 设置内部导航 -->
                    <div class="settings-nav">
                        <button class="settings-nav-btn" data-subtab="agent" data-i18n="agentSettingsNav">助手</button>
                        <button class="settings-nav-btn" data-subtab="model" data-i18n="modelSettingsNav">模型</button>
                        <button class="settings-nav-btn" data-subtab="text-selection-helper" data-i18n="textSelectionHelper">划词助手</button>
                        <button class="settings-nav-btn active" data-subtab="general" data-i18n="generalSettingsNav">通用</button>
                        <!-- 添加关闭按钮 -->
                         <button id="close-panel-settings" title="关闭面板" class="action-button close-panel-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 通用设置 -->
                    <div id="settings-general" class="settings-sub-content active">
                        <h2>通用设置</h2>
                        <div class="setting-group">
                            <label for="language-select">语言：</label>
                            <div class="export-controls">
                                <select id="language-select">
                                    <option value="zh-CN">简体中文</option>
                                    <option value="en">English</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-group export-setting">
                            <label for="export-format">导出聊天记录：</label>
                            <div class="export-controls">
                                <select id="export-format">
                                    <option value="markdown">Markdown</option>
                                    <option value="text">Text</option>
                                </select>
                                <button id="export-chat-history" class="action-button">导出</button>
                            </div>
                        </div>
                        <div class="setting-group">
                            <label for="proxy-address-input" data-i18n="proxyAddressLabel">代理地址：</label>
                            <input type="text" id="proxy-address-input" data-i18n-placeholder="proxyAddressPlaceholder" placeholder="例如: http://127.0.0.1:7890 或 socks5://127.0.0.1:1080">
                            <p class="hint" data-i18n="proxyAddressHint">支持 HTTP 和 SOCKS5 代理，留空则禁用代理。</p>
                        </div>
                    </div>

                    <!-- Agent 设置 -->
                    <div id="settings-agent" class="settings-sub-content">
                        <h2>助手设置</h2>
                        <!-- 助手列表区域 -->
                        <div class="agents-list-container">
                            <div class="agents-list-header">
                                <h3>助手</h3>
                                <!-- 隐藏的文件输入框 (保留) -->
                                <input type="file" id="import-agent-input" accept=".json" style="display: none;">
                            </div>

                            <!-- 新增: Agent 操作按钮栏 -->
                            <div class="agent-actions-bar">
                                <div class="agent-actions-left">
                                <button id="import-agents" class="action-button agent-action-button" data-i18n="importAgentsButton"></button>
                                <button id="export-agents" class="action-button agent-action-button" data-i18n="exportAgentsButton"></button>
                                </div>
                                <div class="agent-actions-right">
                                    <button id="add-new-agent" class="action-button agent-action-button add-agent-button" data-i18n="addNewAgentButton"></button>
                                </div>
                            </div>
                            <!-- 助手列表 -->
                            <div id="agents-list" class="agents-list">
                                <!-- 助手项将通过 JavaScript 动态添加 -->
                            </div>
                        </div>
                        
                        <!-- 删除确认对话框 -->
                        <div id="delete-confirm-dialog" class="dialog-overlay" style="display:none;">
                            <div class="dialog-content">
                                <h3>确认删除</h3>
                                <p>您确定要删除助手 "<span id="delete-agent-name"></span>" 吗？此操作无法撤销。</p>
                                <div class="dialog-actions">
                                    <button id="cancel-delete" class="cancel-btn">取消</button>
                                    <button id="confirm-delete" class="delete-btn">删除</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 划词助手设置 -->
                    <div id="settings-text-selection-helper" class="settings-sub-content">
                        <h2 data-i18n="textSelectionHelperSettings">划词助手设置</h2>

                        <!-- 划词助手开关 -->
                        <div class="text-selection-helper-toggle-container">
                            <span class="toggle-label" data-i18n="textSelectionHelperEnabled">启用划词助手：</span>
                            <label class="toggle-switch">
                                <input type="checkbox" id="text-selection-helper-enabled" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <!-- 解读设置卡片 -->
                        <div class="setting-card">
                            <div class="setting-card-header">
                                <h3>
                                    <svg class="setting-card-icon" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                    <span data-i18n="interpretSettings">解读设置</span>
                                </h3>
                                <button class="setting-card-toggle" type="button">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="setting-card-content">
                                <div class="setting-group">
                                    <label for="interpret-model" data-i18n="model">模型：</label>
                                    <select id="interpret-model">
                                        <!-- 选项将通过 JavaScript 动态填充 -->
                                    </select>
                                </div>
                                <div class="setting-group">
                                    <label for="interpret-system-prompt" data-i18n="systemPrompt">系统提示词：</label>
                                    <textarea id="interpret-system-prompt" rows="3" data-i18n-placeholder="interpretSystemPrompt" placeholder="解读一下"></textarea>
                                </div>
                                <div class="setting-group">
                                    <label for="interpret-temperature" data-i18n="temperature">温度：</label>
                                    <div class="temperature-control">
                                        <input type="range" id="interpret-temperature" min="0" max="1" step="0.1" value="0.7">
                                        <span class="temperature-value">0.7</span>
                                    </div>
                                </div>
                                <div class="setting-group">
                                    <label for="interpret-max-output" data-i18n="maxOutputLength">最大输出长度：</label>
                                    <input type="number" id="interpret-max-output" min="1" max="200000" value="65536" placeholder="65536">
                                </div>
                            </div>
                        </div>

                        <!-- 翻译设置卡片 -->
                        <div class="setting-card">
                            <div class="setting-card-header">
                                <h3>
                                    <svg class="setting-card-icon" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                        <path d="M5 8l6 6"/>
                                        <path d="M4 14l6-6 2-3"/>
                                        <path d="M2 5h12"/>
                                        <path d="M7 2h1"/>
                                        <path d="M22 22l-5-10-5 10"/>
                                        <path d="M14 18h6"/>
                                    </svg>
                                    <span data-i18n="translateSettings">翻译设置</span>
                                </h3>
                                <button class="setting-card-toggle" type="button">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="setting-card-content">
                                <div class="setting-group">
                                    <label for="translate-model" data-i18n="model">模型：</label>
                                    <select id="translate-model">
                                        <!-- 选项将通过 JavaScript 动态填充 -->
                                    </select>
                                </div>
                                <div class="setting-group">
                                    <label for="translate-system-prompt" data-i18n="systemPrompt">系统提示词：</label>
                                    <textarea id="translate-system-prompt" rows="3" data-i18n-placeholder="translateSystemPrompt" placeholder="翻译一下"></textarea>
                                </div>
                                <div class="setting-group">
                                    <label for="translate-temperature" data-i18n="temperature">温度：</label>
                                    <div class="temperature-control">
                                        <input type="range" id="translate-temperature" min="0" max="1" step="0.1" value="0.2">
                                        <span class="temperature-value">0.2</span>
                                    </div>
                                </div>
                                <div class="setting-group">
                                    <label for="translate-max-output" data-i18n="maxOutputLength">最大输出长度：</label>
                                    <input type="number" id="translate-max-output" min="1" max="200000" value="65536" placeholder="65536">
                                </div>
                            </div>
                        </div>

                        <!-- 自定义选项设置 -->
                        <div class="setting-card">
                            <div class="setting-card-header">
                                <h3>
                                    <svg class="setting-card-icon" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <span data-i18n="customOptions">自定义选项</span>
                                </h3>
                                <button class="setting-card-toggle">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="setting-card-content">
                                <div class="custom-options-list" id="custom-options-list">
                                    <!-- 自定义选项将通过 JavaScript 动态添加 -->
                                </div>
                                <button class="add-custom-option-btn" id="add-custom-option-btn">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    <span data-i18n="addCustomOption">添加自定义选项</span>
                                </button>
                            </div>
                        </div>

                        <!-- 选项顺序调整 -->
                        <div class="setting-card">
                            <div class="setting-card-header">
                                <h3>
                                    <svg class="setting-card-icon" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                                        <path d="M3 6h18"/>
                                        <path d="M3 12h18"/>
                                        <path d="M3 18h18"/>
                                    </svg>
                                    <span data-i18n="optionOrder">选项顺序</span>
                                </h3>
                            </div>
                            <div class="setting-card-content">
                                <div class="options-order-list" id="options-order-list">
                                    <!-- 选项顺序将通过 JavaScript 动态添加 -->
                                </div>
                                <p class="hint" data-i18n="dragToReorder">拖拽调整选项在选项栏中的显示顺序</p>
                            </div>
                        </div>
                    </div>

                    <!-- 模型设置 -->
                    <div id="settings-model" class="settings-sub-content">
                        <h2>模型设置</h2>
                        <div class="model-settings-group-box">
                            <div class="setting-group">
                                <label for="api-key">Gemini API Key:</label>
                                <div class="api-key-input-container">
                                    <input type="password" id="api-key" placeholder="输入您的API Key">
                                    <button id="toggle-api-key" class="toggle-api-key-button" type="button" title="Toggle API Key visibility">
                                        <svg id="eye-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye" viewBox="0 0 16 16">
                                            <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.1 13.1 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.1 13.1 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.1 13.1 0 0 1 1.172 8z"/>
                                            <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
                                        </svg>
                                        <svg id="eye-slash-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye-slash" viewBox="0 0 16 16" style="display: none;">
                                            <path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"/>
                                            <path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"/>
                                            <path d="M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"/>
                                        </svg>
                                    </button>
                                </div>
                                <p class="hint"><span data-i18n="apiKeyHint"></span>  <a href="https://aistudio.google.com/" target="_blank" rel="noopener">Google AI Studio</a></p>
                            </div>
                            <div class="setting-group">
                                <label for="model-selection">模型：</label>
                                <select id="model-selection">
                                    <option value="gemini-2.0-flash">gemini-2.0-flash</option>
                                    <option value="gemini-2.0-flash-thinking-exp-01-21">gemini-2.0-flash-thinking</option>
                                    <option value="gemini-2.0-pro-exp-02-05">gemini-2.0-pro-exp-02-05</option>
                                    <option value="gemini-exp-1206">gemini-exp-1206</option>
                                    <option value="gemini-2.5-pro-exp-03-25">gemini-2.5-pro-exp-03-25</option>
                                    <option value="gemini-2.5-pro-preview-03-25">gemini-2.5-pro-preview-03-25</option>
                                    <option value="gemini-2.5-pro-preview-05-06">gemini-2.5-pro-preview-05-06</option>
                                </select>
                            </div>
                        </div>
                        <div class="model-save-area">
                            <button id="save-model-settings" class="save-btn">保存</button>
                            <div id="connection-status" class="connection-status"></div>
                        </div>
                    </div>
                </div>
                <!-- Moved Theme Toggle Button -->
                <button id="theme-toggle-btn" class="icon-btn theme-toggle-btn" title="切换深色/浅色模式">
                    <!-- Moon Icon (Default) -->
                    <svg id="moon-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278zM10.794 3.148a.217.217 0 0 1 .412 0l.387 1.162h1.212a.217.217 0 0 1 .154.371l-.98.752.387 1.162a.217.217 0 0 1-.316.228l-.98-.752-.98.752a.217.217 0 0 1-.316-.228l.387-1.162-.98-.752a.217.217 0 0 1 .154-.371h1.212l.387-1.162zM13.863.099a.145.145 0 0 1 .274 0l.258.774c.115.346.386.617.732.732l.774.258a.145.145 0 0 1 0 .274l-.774.258a1.156 1.156 0 0 0-.732.732l-.258.774a.145.145 0 0 1-.274 0l-.258-.774a1.156 1.156 0 0 0-.732-.732l-.774-.258a.145.145 0 0 1 0-.274l.774-.258c.346-.115.617-.386.732-.732L13.863.1z"/>
                    </svg>
                    <!-- Sun Icon (Hidden initially) -->
                    <svg id="sun-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16" style="display: none;">
                        <path d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707z"/>
                    </svg>
                </button>
            </section>
        </main>

        <footer>
            <div class="status-bar">
                <span id="context-status">Context: 无</span>
                <span id="connection-indicator" class="disconnected">未连接</span>
            </div>
            <!-- 更新后的底部导航栏 -->
            <ul class="footer-tabs">
                <li class="footer-tab active" data-tab="chat">聊天</li>
                <li class="footer-tab" data-tab="settings">设置</li>
            </ul>
        </footer>
    </div>
    
    <!-- 新增：更新通告模态框 -->
    <div id="changelog-modal" class="changelog-modal">
        <div class="changelog-content">
            <div class="changelog-header">
                <h2 id="changelog-title">更新</h2>
                <p id="changelog-subtitle"></p>
            </div>
            <div class="changelog-list" id="changelog-list">
                <!-- 更新记录将通过JavaScript动态填充 -->
            </div>
            <div class="changelog-footer">
                <div class="changelog-checkbox">
                    <input type="checkbox" id="never-show-checkbox">
                    <label for="never-show-checkbox" id="never-show-label">不再显示本版本更新</label>
                </div>
                <button id="changelog-ok-btn" class="changelog-ok-btn">OK</button>
            </div>
        </div>
    </div>
    
    <!-- 新增：Mermaid 图表放大预览模态框 -->
    <div id="mermaid-modal" class="mermaid-modal">
        <span class="close-modal mermaid-close-modal">&times;</span>
        <div id="mermaid-modal-content" class="modal-content mermaid-modal-content">
            <!-- 放大的 Mermaid SVG 将被注入这里 -->
        </div>
    </div>

    <!-- Day.js Core & Locales -->
    <script src="../js/lib/dayjs.min.js"></script>
    <script src="../js/lib/en.min.js"></script>
    <script src="../js/lib/zh-cn.min.js"></script>
    <!-- KaTeX Core -->
    <script defer src="../js/lib/katex.min.js"></script>
    <!-- KaTeX Auto-render Extension -->
    <script defer src="../js/lib/auto-render.min.js"></script>
    <!-- KaTeX mhchem Extension (Optional, for chemistry) -->
    <script defer src="../js/lib/mhchem.min.js"></script>
    <!-- Mermaid JS -->
    <script src="../js/lib/mermaid.min.js"></script>
    <!-- 新增：加载 Panzoom 库 -->
    <script src="../js/lib/panzoom.min.js"></script>
    <!-- Load Helper Modules BEFORE main.js -->
    <script src="../js/api.js"></script>
    <script src="../js/translations.js"></script>
    <!-- 新增: 加载更新日志模块 -->
    <script src="../js/changelog.js"></script>
    <!-- Load the main coordinator script as a module -->
    <script type="module" src="../js/main.js"></script>
</body>
</html>
