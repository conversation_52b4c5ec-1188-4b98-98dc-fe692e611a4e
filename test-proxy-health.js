/**
 * PageTalk 代理健康检查测试脚本
 * 
 * 这个脚本可以帮助测试代理健康检查功能是否正常工作
 * 使用方法：在浏览器控制台中运行此脚本
 */

// 测试代理健康检查功能
async function testProxyHealthCheck() {
    console.log('🔍 开始测试代理健康检查功能...');
    
    // 1. 检查是否有代理设置
    try {
        const result = await chrome.storage.sync.get(['proxyAddress']);
        const proxyAddress = result.proxyAddress;
        
        if (!proxyAddress) {
            console.log('❌ 没有设置代理地址，请先在PageTalk设置中配置代理');
            return;
        }
        
        console.log('✅ 当前代理地址:', proxyAddress);
        
        // 2. 检查代理设置是否已应用到Chrome
        const proxySettings = await chrome.proxy.settings.get({});
        console.log('🔧 Chrome代理设置:', proxySettings);
        
        // 3. 模拟健康检查
        console.log('🏥 开始模拟健康检查...');
        
        const testUrl = 'https://www.google.com/generate_204';
        const startTime = Date.now();
        
        try {
            const response = await fetch(testUrl, {
                method: 'HEAD',
                cache: 'no-cache',
                signal: AbortSignal.timeout(3000)
            });
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            if (response.ok) {
                console.log(`✅ 代理健康检查成功! 响应时间: ${duration}ms`);
                console.log('📊 响应状态:', response.status, response.statusText);
            } else {
                console.log(`❌ 代理健康检查失败! HTTP ${response.status}: ${response.statusText}`);
            }
            
        } catch (fetchError) {
            console.log('❌ 代理健康检查失败:', fetchError.message);
            console.log('💡 这种情况下，实际的健康检查会增加失败计数（连续2次失败后自动清理）');
        }
        
        // 4. 检查background.js中的健康检查状态
        console.log('📡 向background.js发送测试消息...');
        
        chrome.runtime.sendMessage({
            action: 'getProxyHealthStatus'
        }, (response) => {
            if (response) {
                console.log('📈 代理健康状态:', response);
            } else {
                console.log('⚠️ 无法获取代理健康状态（可能需要在background.js中添加相应处理）');
            }
        });
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    }
}

// 测试代理自动清除功能
async function testProxyAutoClear() {
    console.log('🧹 开始测试代理自动清除功能...');
    
    try {
        // 模拟发送代理自动清除消息
        const testProxy = 'http://127.0.0.1:7890';
        
        // 向content script发送消息
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'proxyAutoCleared',
                    failedProxy: testProxy
                }, (response) => {
                    console.log('📤 已发送代理自动清除测试消息');
                });
            }
        });
        
    } catch (error) {
        console.error('❌ 测试代理自动清除时发生错误:', error);
    }
}

// 监控代理设置变化
function monitorProxyChanges() {
    console.log('👀 开始监控代理设置变化...');
    
    // 监听存储变化
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'sync' && changes.proxyAddress) {
            console.log('🔄 代理地址发生变化:');
            console.log('  旧值:', changes.proxyAddress.oldValue);
            console.log('  新值:', changes.proxyAddress.newValue);
        }
    });
    
    // 定期检查Chrome代理设置
    setInterval(async () => {
        try {
            const settings = await chrome.proxy.settings.get({});
            console.log('⏰ 当前Chrome代理设置:', settings.value);
        } catch (error) {
            console.error('❌ 获取代理设置失败:', error);
        }
    }, 5000); // 每5秒检查一次
}

// 清除所有代理设置（用于测试）
async function clearAllProxySettings() {
    console.log('🧽 清除所有代理设置...');
    
    try {
        // 清除Chrome代理设置
        await chrome.proxy.settings.clear({});
        console.log('✅ Chrome代理设置已清除');
        
        // 清除存储中的代理设置
        await chrome.storage.sync.remove('proxyAddress');
        console.log('✅ 存储中的代理设置已清除');
        
    } catch (error) {
        console.error('❌ 清除代理设置失败:', error);
    }
}

// 导出测试函数到全局作用域
window.testProxyHealthCheck = testProxyHealthCheck;
window.testProxyAutoClear = testProxyAutoClear;
window.monitorProxyChanges = monitorProxyChanges;
window.clearAllProxySettings = clearAllProxySettings;

console.log(`
🧪 PageTalk 代理健康检查测试工具已加载

可用的测试函数：
- testProxyHealthCheck()     : 测试代理健康检查
- testProxyAutoClear()       : 测试代理自动清除通知
- monitorProxyChanges()      : 监控代理设置变化
- clearAllProxySettings()    : 清除所有代理设置

使用示例：
  testProxyHealthCheck();
  monitorProxyChanges();
`);
